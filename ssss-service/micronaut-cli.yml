applicationType: default
defaultPackage: com.digoak.ssss
testFramework: junit
sourceLanguage: java
buildTool: maven
features: [ app-name, data, data-jpa, http-client, jackson-databind, java, java-application, jdbc-hikari, jul-to-slf4j, junit, liquibase, logback, lombok, maven, maven-enforcer-plugin, micronaut-aot, micronaut-http-validation, mysql, netty-server, readme, shade, static-resources, test-resources, yaml, yaml-build ]
