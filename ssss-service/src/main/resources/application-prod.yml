micronaut:
  application:
    name: oakSsss
  server:
    cors:
      enabled: true
    context-path: /oak-ssss
    port: 8080
    client:
      read:
        timeout: 5s
  http:
    client:
      connect-timeout: 3s
      read-timeout: 5s # 全局读取超时时间
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 30
  test:
    resources:
      enabled: false

jpa:
  default:
    properties:
      hibernate:
        hbm2ddl:
          auto: update
        show_sql: true

datasources:
  default:
    driverClassName: com.mysql.cj.jdbc.Driver
    db-type: mysql
    schema-generate: UPDATE
    url: *************************************************************************************************************************************************************
    username: root
    password: digoakroot127DA
    pooled: true
    maximum-pool-size: 50
    minimum-idle: 5
netty:
  default:
    allocator:
      max-order: 3
liquibase:
  enabled: false
  datasources:
    default:
      change-log: classpath:db/liquibase-changelog.xml
shedlock:
  defaults:
    lock-at-most-for: 1m
