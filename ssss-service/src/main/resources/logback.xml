<configuration>
    <contextName>oak-ssss</contextName>
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator"/>
    <property name="log.home" value="/usr/digoak/apps/oak-ssss/logs" />
    <property name="log.charset" value="utf-8"/>
    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
    <property name="log.pattern"
              value="%d{yyyy-MM-dd HH:mm:ss} %green([%contextName/%thread]) %highlight(%-5level) %boldMagenta(%logger{50}) - %msg%n"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>%cyan(%d{yyyy-MM-dd HH:mm:ss.SSS}) %gray([%thread]) %highlight(%-5level) %magenta(%logger{36}) - %msg%n</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>
    <!-- 按照每月生成日志文件 -->
    <appender name="FILE"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>logs/oak-ssss-%d{yyyy-MM}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <charset>${log.charset}</charset>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <immediateFlush>true</immediateFlush>
    </appender>

    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
