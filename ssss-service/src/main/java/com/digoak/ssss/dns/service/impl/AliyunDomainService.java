package com.digoak.ssss.dns.service.impl;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.digoak.ssss.dns.domain.Domain;
import com.digoak.ssss.dns.repository.DomainRepository;
import com.digoak.ssss.dns.service.QueryUsableService;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 域名服务
 */
@Slf4j
@Singleton
public class AliyunDomainService implements QueryUsableService {

    @Inject
    private DomainRepository domainRepository;

    @Override
    public String operateType() {
        return AliyunDomainService.class.getSimpleName();
    }

    /**
     * domain=csteat.com,cc.com&token=Y686c1a2c063bf5e9e22ad22c72564c67&callback=jsonp_1576167480148_89958
     * 查询可用域名
     * @param domains
     * @return
     */
    @Override
    @Transactional
    public JSONObject queryUsableDomain(String domains) {
        log.info("Aliyun Thread {} start {}", Thread.currentThread().getName(), System.currentTimeMillis());

        UrlBuilder uri = UrlBuilder.of("https://checkapi.aliyun.com/check/checkdomain")
                .addQuery("token", "Y7bad4ba702f9d0765a5913f86e3cb349")
                .addQuery("domain", domains);

        // Cookie: x5sec;tmp0
        String result = HttpRequest.of(uri)
                .header(Header.USER_AGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36")
                .header(Header.REFERER, "https://wanwang.aliyun.com/domain/")
                .header(Header.COOKIE, "tmp0=c8WhVh5Avk6gEEWwyjscNwb7eEoqjq%2FYbF1YIoA6KdyHmk8RR6vDsMfoV7P%2BJYRe3hNuBjNI40gKk131X2zysBUnJaS%2FoYmfPAmoL8Ef1ARdL4bfeiXiF4Ir6pbLnKpfKZhdgEjlJ1eaG%2FYxE41gvg%3D%3D; x5sec=7b22746f6e6779696a696572752d616c6979756e2d636f6d2d74616e6e65693b32223a22323065396231383635353939643538343236383965383961373933386430643643507562784a6b47454f584c2b62794c68387147436a436775375879426b4144227d")
                .execute().body();
        log.info("domain params: {}, result: {}", domains, result);
        JSONObject jsonObj = null;
        if(StringUtils.isNotEmpty(result)) {
            int beginIndex = result.indexOf("(");
            result = result.substring(beginIndex + 1, result.length() - 2);
            jsonObj = JSONUtil.parseObj(result);
            if(jsonObj.getStr("success").equals("true")) {
                List<Domain> domainList = new ArrayList<>();
                JSONArray module = jsonObj.getJSONArray("module");
                module.forEach(it -> {
                    JSONObject ito = JSONUtil.parseObj(it);
                    if (ito.getInt("avail") == 1) {
                        Domain domain = new Domain();
                        domain.setName(ito.getStr("name"));
                        domain.setSuffix(ito.getStr("tld"));
                        domain.setLength(domain.getName().length() - domain.getSuffix().length());
                        domain.setUsable(true);
                        domainList.add(domain);
                    }
                });
                if(!CollectionUtils.isEmpty(domainList)) {
                    domainRepository.saveAll(domainList);
                }
            }else {
                log.error("queryUsableDomain failed {}", "aliyun");
            }
        }
        log.info("Aliyun Thread {} end {}", Thread.currentThread().getName(), System.currentTimeMillis());
        return jsonObj;
    }
}
