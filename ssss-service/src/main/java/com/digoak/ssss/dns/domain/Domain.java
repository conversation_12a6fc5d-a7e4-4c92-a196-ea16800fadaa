package com.digoak.ssss.dns.domain;

import com.digoak.boot.platform.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/10
 * @description 域名
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "d_domain")
public class Domain extends BaseDomain {

    @Column(length = 64, nullable = false, unique = true)
    private String name; // 域名

    @Column(length = 16)
    private String suffix;   // 后缀

    private Integer length;  // 长度
    private Boolean usable;  // 是否可用
}
