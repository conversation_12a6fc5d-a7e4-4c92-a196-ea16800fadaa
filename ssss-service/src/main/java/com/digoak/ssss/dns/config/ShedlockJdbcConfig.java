package com.digoak.ssss.dns.config;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/16
 * @description 该类为配置类
 */

import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.transaction.TransactionOperations;
import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.jdbc.micronaut.MicronautJdbcLockProvider;

import java.sql.Connection;

@Factory
public class ShedlockJdbcConfig {

    @Bean
    public LockProvider lockProvider(TransactionOperations<Connection> transactionManager) {
        return new MicronautJdbcLockProvider(transactionManager);
    }
}
