package com.digoak.ssss.dns.crontab;

import com.digoak.ssss.dns.service.DomainService;
import io.micronaut.scheduling.annotation.Scheduled;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.micronaut.SchedulerLock;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/16
 * @description 域名定时任务
 */
@Slf4j
@Singleton
public class DomainTask {

    @Inject
    private DomainService domainService;

    @Scheduled(cron = "2 9 1 ? * WED,SAT")
    @SchedulerLock(name = "alphabetWordsQuery")
    public void alphabetWordsQuery() {
        log.info("begin domain usable job {}", new Date());

        domainService.alphabetQuery(4, null);
//        domainService.alphabetWordsQuery(1, 4);

        log.info("end domain usable job {}", new Date());
    }
}
