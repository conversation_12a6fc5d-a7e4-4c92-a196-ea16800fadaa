package com.digoak.ssss.dns.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.digoak.ssss.dns.factory.QueryUsableFactory;
import com.digoak.ssss.english.domain.EnglishWords;
import com.digoak.ssss.english.service.EnglishWordsService;
import io.micronaut.scheduling.annotation.Async;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 域名服务
 */
@Slf4j
@Singleton
public class DomainService {

    private final String suffix = ".com";
    private final String operateType = "NowDomainService";
    private final int maxBatchNum = 40;
    private final String[] alphabetArray = "abcdefghijklmnopqrstuvwxyz".split("");
    private List<String> totalDomains = new ArrayList<>();

    @Inject
    private EnglishWordsService englishWordsService;
    @Inject
    private QueryUsableFactory queryUsableFactory;

    @Async
    @Transactional
    public void asyncQueryUsableDomain(String operateType, String domains) {
        queryUsableFactory.queryUsableDomain(operateType, domains);
    }

    /**
     * 异步批量查询域名
     * @param domainList
     * @return
     */
    public void checkExecDomain(List<String> domainList) {
        if (CollectionUtils.isEmpty(domainList)) {
            return;
        }

        if (domainList.size() > maxBatchNum) {
            int threadNum = 0;
            while (!domainList.isEmpty()) {
                int fromIndex = 0;
                int toIndex = Math.min(domainList.size(), maxBatchNum);
                List<String> tempList = domainList.subList(fromIndex, toIndex);
                asyncQueryUsableDomain(operateType, StringUtils.join(tempList, ","));
                ++threadNum;
                if (threadNum >= 20) {
                    threadNum = 0;
                    try {
                        Thread.sleep(RandomUtil.randomInt(2000, 4000));
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                domainList = domainList.subList(toIndex, domainList.size());
            }
        } else {
            asyncQueryUsableDomain(operateType, StringUtils.join(domainList, ","));
        }

    }

    /**
     * 根据字母组合查询
     * @param maxLen
     * @param domains
     * @return
     */
    public void alphabetQuery(int maxLen, List<String> domains) {
        if (maxLen <= 0) {
            log.info("handle totalDomains size: {}", totalDomains.size());
        } else {
            List<String> queryDomains = new ArrayList<>();
            for (String alphabet : alphabetArray) {
                if (domains != null && !domains.isEmpty()) {
                    for (String domain : domains) {
                        queryDomains.add(alphabet + domain);
                    }
                } else {
                    if (CollUtil.isNotEmpty(totalDomains)) {
                        totalDomains = new ArrayList<>();
                    }
                    queryDomains.add(alphabet + suffix);
                }
            }

            totalDomains.addAll(queryDomains);
            --maxLen;
            checkExecDomain(queryDomains);
            alphabetQuery(maxLen, queryDomains);
        }
    }

    /**
     * 查询英文单词域名
     * @param domainMinLen
     * @param domainMaxLen
     * @return
     */
    public void alphabetWordsQuery(Integer domainMinLen, Integer domainMaxLen) {
        if (domainMinLen <= 0) {
            log.warn("handle alphabetWordsQuery domainLen: {} - {}", domainMinLen, domainMaxLen);
        } else {
            List<EnglishWords> englishWordsList = englishWordsService.queryByLength(domainMinLen, domainMaxLen);
            if (!CollectionUtils.isEmpty(englishWordsList)) {
                List<String> queryDomains = englishWordsList.stream().map(e -> {
                    return e.getWord().concat(suffix);
                }).collect(Collectors.toList());
                checkExecDomain(queryDomains);
            }
            log.info("handle alphabetWordsQuery Domains size: {}", englishWordsList.size());
        }
    }
}
