package com.digoak.ssss.dns.factory;

import cn.hutool.json.JSONObject;
import com.digoak.ssss.dns.service.QueryUsableService;
import io.micronaut.context.BeanProvider;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @Date 2024/4/5
 * @Description 查询工厂
 **/
@Slf4j
@Singleton
public class QueryUsableFactory {
    /**
     * Spring会自动将Strategy接口的实现类注入到这个Map中，key为bean id，value值则为对应的策略实现类
     */
    @Inject
    private BeanProvider<QueryUsableService> strategyProvider;


    /**
     * 执行查询
     * @param operateType
     * @param domains
     * @return JSONObject
     */
    public JSONObject queryUsableDomain(String operateType, String domains) {
        if(StringUtils.isBlank(operateType)) {
            throw new IllegalArgumentException("Invalid Operator");
        }
        AtomicReference<JSONObject> result = new AtomicReference<>();
        if(strategyProvider.isPresent()) {
            strategyProvider.stream().filter(qus -> qus.operateType().equals(operateType)).findFirst().ifPresent(qu -> {
                log.info("{}, {}", qu.operateType(), Thread.currentThread().getName());
                result.set(qu.queryUsableDomain(domains));
            });
        }
        if(Objects.isNull(result.get())) {
            log.error("Invalid Operator queryUsableDomain {}", Thread.currentThread().getName());
        }
        return result.get();
    }
}
