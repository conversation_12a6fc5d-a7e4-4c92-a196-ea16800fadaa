package com.digoak.ssss.dns.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.digoak.boot.platform.constants.BaseConstants;
import com.digoak.ssss.dns.domain.Domain;
import com.digoak.ssss.dns.repository.DomainRepository;
import com.digoak.ssss.dns.service.QueryUsableService;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 域名服务
 */
@Slf4j
@Singleton
public class NowDomainService implements QueryUsableService {

    @Inject
    private DomainRepository domainRepository;

    @Override
    public String operateType() {
        return NowDomainService.class.getSimpleName();
    }

    /**
     * https://checkapi.now.cn:8080/?domains=digoak.com,siegislie.com
     * 查询可用域名
     * @param domains
     * @return status: 1 可购买，2 被使用
     * {
     *     "code": 100,
     *     "err": "",
     *     "results": [
     *         {
     *             "domain": "digoak.com",
     *             "domain_keycode": "6fa03e37a41a94ed31d0a90b25adac6d",
     *             "premium": 0,
     *             "price": "",
     *             "status": 2
     *         }
     *     ]
     * }
     */
    @Override
    @Transactional
    public JSONObject queryUsableDomain(String domains) {
        log.info("Now Thread {} start {}", Thread.currentThread().getName(), DateUtil.now());

        UrlBuilder uri = UrlBuilder.of("https://checkapi.now.cn:8080/")
                .addQuery("domains", domains);

        // Cookie: x5sec;tmp0
        String result = HttpRequest.of(uri)
                .header(Header.USER_AGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36")
                .header(Header.REFERER, "https://checkapi.now.cn:8080/")
                .execute().body();
        JSONObject jsonObj = null;
        if (StringUtils.isNotEmpty(result)) {
            jsonObj = JSONUtil.parseObj(result);
            if(jsonObj.getInt("code") == 100) {
                List<Domain> domainList = new ArrayList<>();
                JSONArray module = jsonObj.getJSONArray("results");
                String newDomain = null;
                for(Object it : module) {
                    JSONObject ito = JSONUtil.parseObj(it);
                    if(ito.getInt("status") == 1) {
                        Domain domain = new Domain();
                        newDomain = ito.getStr("domain");
                        domain.setName(newDomain);
                        domain.setSuffix(newDomain.substring(newDomain.lastIndexOf(BaseConstants.SEPARATOR_DOT)));
                        domain.setLength(domain.getName().length() - domain.getSuffix().length());
                        domain.setUsable(true);
                        domainList.add(domain);
                    }
                }
                if(!CollectionUtils.isEmpty(domainList)) {
                    domainRepository.saveAll(domainList);
                }
            }else {
                log.error("queryUsableDomain failed {}", jsonObj.getStr("err"));
            }
        } else {
            log.info("queryUsableDomain failed domain params: {}, result: {}", domains, result);
        }
        log.info("Now Thread {} end {}", Thread.currentThread().getName(), DateUtil.now());
        return jsonObj;
    }
}
