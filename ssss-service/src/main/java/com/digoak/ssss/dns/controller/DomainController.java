package com.digoak.ssss.dns.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.digoak.ssss.dns.factory.QueryUsableFactory;
import com.digoak.ssss.dns.service.DomainService;
import io.micronaut.core.async.annotation.SingleResult;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.validation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Tag(name = "域名接口")
@RequiredArgsConstructor
@Controller("/dns")
@Slf4j
@Validated
public class DomainController {

    private final DomainService domainService;
    private final QueryUsableFactory queryUsableFactory;

    @Get(value = "/queryUsableDomain", produces = MediaType.APPLICATION_JSON)
    @SingleResult
    public HttpResponse<JSONObject> queryUsableDomain(String domains, String operateType) {

        log.info("begin domain usable Controller {}", DateUtil.now());

        JSONObject jsonObj = queryUsableFactory.queryUsableDomain(operateType, domains);

        log.info("end domain usable Controller {}", DateUtil.now());
        return HttpResponse.ok(jsonObj);
    }

    @Get(value = "/alphabetQuery", produces = MediaType.APPLICATION_JSON)
    @SingleResult
    public HttpResponse<String> alphabetQuery(@NotNull(message = "域名长度不能为空") Integer maxLength) {

        domainService.alphabetQuery(maxLength, null);
        return HttpResponse.ok("ok");
    }

    @Get(value = "/alphabetWordsQuery", produces = MediaType.APPLICATION_JSON)
    public HttpResponse<String> alphabetWordsQuery(@NotNull Integer domainMinLength, @NotNull Integer domainMaxLength) {

        log.info("begin domain alphabetWordsQuery Controller {}", new Date());

        domainService.alphabetWordsQuery(domainMinLength, domainMaxLength);

        log.info("end domain alphabetWordsQuery Controller {}", new Date());
        return HttpResponse.ok("ok");
    }
}
