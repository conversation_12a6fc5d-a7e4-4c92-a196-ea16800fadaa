package com.digoak.ssss.english.controller;

import com.digoak.ssss.english.domain.EnglishWords;
import com.digoak.ssss.english.service.EnglishWordsService;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@Tag(name = "英文单词控制")
@RequiredArgsConstructor
@Controller("/englishWords")
public class EnglishWordsController {

    private final EnglishWordsService englishWordsService;

    @Get(uri = "/", produces = MediaType.TEXT_PLAIN)
    public String index() {
        return "Hello World";
    }

    @Get(value = "/queryByPage", produces = MediaType.APPLICATION_JSON)
    public HttpResponse<Page<EnglishWords>> queryByPage(String word, Pageable pageable) {
        Page<EnglishWords> page = englishWordsService.queryByPage("%".concat(word).concat("%"), pageable);
        return HttpResponse.ok(page);
    }
}
