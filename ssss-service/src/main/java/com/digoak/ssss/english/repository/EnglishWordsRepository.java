package com.digoak.ssss.english.repository;

import com.digoak.ssss.english.domain.EnglishWords;
import io.micronaut.data.annotation.Query;
import io.micronaut.data.annotation.Repository;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import io.micronaut.data.repository.CrudRepository;
import io.micronaut.data.repository.jpa.JpaSpecificationExecutor;

import java.util.List;

@Repository
public interface EnglishWordsRepository extends CrudRepository<EnglishWords, Long>, JpaSpecificationExecutor<EnglishWords> {

    Page<EnglishWords> findByWordLike(String word, Pageable pageable);

    @Query(value = "from EnglishWords where LENGTH(word) <= :maxLength and LENGTH(word) >= :minLength")
    List<EnglishWords> findByWordLengthBetween(Integer minLength, Integer maxLength);

}
