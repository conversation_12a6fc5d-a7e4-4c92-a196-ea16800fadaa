package com.digoak.ssss.english.service.impl;

import com.digoak.ssss.english.domain.EnglishWords;
import com.digoak.ssss.english.repository.EnglishWordsRepository;
import com.digoak.ssss.english.service.EnglishWordsService;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/9
 * @description 英文单词服务实现
 */
@Singleton
public class EnglishWordsServiceImpl implements EnglishWordsService {

    @Inject
    private EnglishWordsRepository englishWordsRepository;

    @Override
    public Page<EnglishWords> queryByPage(String word, Pageable pageable) {
        return englishWordsRepository.findByWordLike(word, pageable);
    }

    @Override
    public List<EnglishWords> queryByLength(Integer minLength, Integer maxLength) {
        return englishWordsRepository.findByWordLengthBetween(minLength, maxLength);
    }
}
