package com.digoak.ssss.english.domain;

import com.digoak.boot.platform.base.BaseDomain;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/9
 * @description 英文单词
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "e_english_words")
public class EnglishWords extends BaseDomain {

    @SchemaProperty(name = "单词")
    @NotNull
    @Column(length = 64, nullable = false, unique = true)
    private String word;

    @SchemaProperty(name = "开始字母")
    private Character initialLetter;

    @SchemaProperty(name = "音标")
    @Column(length = 128)
    private String phoneticSymbol;

    @SchemaProperty(name = "翻译")
    private String translation;
}
