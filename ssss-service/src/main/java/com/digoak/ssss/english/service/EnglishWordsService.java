package com.digoak.ssss.english.service;

import com.digoak.ssss.english.domain.EnglishWords;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/9
 * @description 英文单词服务
 */
public interface EnglishWordsService {

    /**
     * 根据单词查询
     * @param word
     * @param pageable
     * @return Page
     */
    Page<EnglishWords> queryByPage(String word, Pageable pageable);

    /**
     * 返回查询长度内的单词列表
     * @param minLength
     * @param maxLength
     * @return List
     */
    List<EnglishWords> queryByLength(Integer minLength, Integer maxLength);
}
