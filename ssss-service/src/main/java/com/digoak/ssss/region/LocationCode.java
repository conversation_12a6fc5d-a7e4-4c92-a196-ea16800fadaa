package com.digoak.ssss.region;

import io.swagger.v3.oas.annotations.media.SchemaProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date
 * @description 省市区行政区域
 */

@Getter
@Setter
@ToString
@Entity
@Table(name = "r_location_code")
public class LocationCode {

    @Id
    @NotNull
    @Column(updatable = false)
    private Long code;

    @Column(length = 64)
    private String name;

    private Long pCode;

    @SchemaProperty(name = "区号（0571）")
    @Column(length = 8)
    private String areaCode;

    @SchemaProperty(name = "首字母")
    private Character initialsLetter;

    @SchemaProperty(name = "全编码")
    private Long extCode;

    @Column(length = 64)
    private String extName;

    @SchemaProperty(name = "地区级别 1-省、自治区、直辖市 2-地级市、地区、自治州、盟 3-市辖区、县级市、县")
    private Integer deep;

}
