package com.digoak.ssss.region;

import com.digoak.boot.platform.base.BaseDomain;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date
 * @description 地理位置
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "r_geolocation")
public class Geolocation extends BaseDomain {

    @SchemaProperty(name = "省编码")
    private Long provinceCode;

    private Long cityCode;

    private Long areaCode;

    @SchemaProperty(name = "地址")
    private String address;

    @SchemaProperty(name = "经度")
    private Double longitude;
    private Double latitude;
}
