package com.digoak.ssss.taoworld.controller;

import io.micronaut.core.async.annotation.SingleResult;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.client.HttpClient;
import io.micronaut.http.client.annotation.Client;
import io.micronaut.validation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.URL;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Tag(name = "淘宝跨境接口")
@RequiredArgsConstructor
@Controller("/tao")
@Slf4j
@Validated
public class TaoWorldController {

    private static final Pattern PATTERN_URL = Pattern.compile("var\\s+url\\s*=\\s*(\"(.*)\"|\'(.*)\')");

    @Client
    @Inject
    HttpClient client;

    @Get(value = "/convertToPcUrl", produces = MediaType.APPLICATION_JSON)
    @SingleResult
    public HttpResponse<String> alphabetQuery(@NotNull(message = "URL地址不能为空") @URL(message = "URL格式不正确") String mobileUrl) {
        String pcHtml = client.toBlocking().retrieve(mobileUrl);
        Document doc = Jsoup.parse(pcHtml);
        Elements scriptElements = doc.getElementsByTag("script");
        String pcURL = null;
        for (Element script : scriptElements) {
            String scriptContent = script.html();

            // 提取变量的值
            pcURL = extractVariableValue(scriptContent);
            log.info("The value of pcURL is: {}", pcURL);
            if(pcURL.indexOf("//") != -1) {
                break;
            }
        }
        return HttpResponse.ok(pcURL);
    }

    private static String extractVariableValue(String scriptContent) {
        // 使用正则表达式匹配变量赋值
        String varAssignment = "";
        Matcher matcher = PATTERN_URL.matcher(scriptContent);
        if (matcher.find()) {
            varAssignment = matcher.group(1).substring(1, varAssignment.length() - 1);
        }
        return varAssignment;
    }
}
