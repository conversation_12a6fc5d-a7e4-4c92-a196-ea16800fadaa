package com.digoak.ssss.house.domian;

import com.digoak.boot.platform.base.BaseDomain;
import com.digoak.ssss.region.Geolocation;
import io.swagger.v3.oas.annotations.media.SchemaProperty;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/10
 * @description 房价
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "h_housing_price")
public class HousingPrice extends BaseDomain {

    @SchemaProperty(name = "年份")
    @Min(value = 1900, message = "年份格式不正确")
    @Max(value = 2100, message = "年份格式不正确")
    private Integer year;

    @SchemaProperty(name = "月份")
    @Min(value = 1, message = "月份格式不正确")
    @Max(value = 12, message = "月份格式不正确")
    private Integer month;

    @SchemaProperty(name = "均价(万元)")
    @Column(scale = 2)
    private Double averagePrice;

    @SchemaProperty(name = "位置")
    @OneToOne
    private Geolocation location;
}
