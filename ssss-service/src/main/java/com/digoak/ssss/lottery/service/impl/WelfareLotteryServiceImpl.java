package com.digoak.ssss.lottery.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.digoak.boot.platform.constants.BaseConstants;
import com.digoak.ssss.lottery.domain.WelfareLottery;
import com.digoak.ssss.lottery.repository.WelfareLotteryRepository;
import com.digoak.ssss.lottery.service.WelfareLotteryService;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/10/15
 * @Description
 **/
@Service
@Slf4j
public class WelfareLotteryServiceImpl implements WelfareLotteryService {

    @Inject
    private WelfareLotteryRepository welfareLotteryRepository;

    @Override
    @Transactional
    public void crawlData(Date startDate, Date endDate) {
        UrlBuilder uri = UrlBuilder.of("https://www.cwl.gov.cn/cwl_admin/front/cwlkj/search/kjxx/findDrawNotice?name=ssq&issueCount=50");
        if(Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            uri.addQuery("dayStart", DateUtil.formatDate(startDate));
            uri.addQuery("dayEnd", DateUtil.formatDate(endDate));
        }

        String result = HttpRequest.of(uri)
                .header(Header.USER_AGENT, "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .header(Header.REFERER, "https://www.cwl.gov.cn/ygkj/wqkjgg/")
                .header(Header.ACCEPT, "application/json, text/javascript, */*; q=0.01")
                .header(Header.HOST, "www.cwl.gov.cn")
                .execute().body();
        if(StringUtils.isNotEmpty(result)) {
            if(result.startsWith("<html>")) {
                log.info("crawlData result: {}", result);
                try {
                    Thread.sleep(RandomUtil.randomLong(10000, 30000));
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                this.crawlData(startDate, endDate);
                return;
            }
            JSONObject jsonObj = JSONUtil.parseObj(result);
            if(jsonObj.getInt("state") == 0) {
                Long maxId = welfareLotteryRepository.findMaxId();
                List<WelfareLottery> newLotteryList = new ArrayList<>();
                JSONArray module = jsonObj.getJSONArray("result");
                String[] redArray;
                for(Object it : module) {
                    JSONObject ito = JSONUtil.parseObj(it);
                    if(Objects.isNull(maxId) || ito.getLong("code") > maxId) {
                        WelfareLottery welfareLottery = new WelfareLottery();
                        welfareLottery.setId(ito.getLong("code"));
                        welfareLottery.setDrawingTime(DateUtil.parseDate(ito.getStr("date").substring(0, 10)));
                        welfareLottery.setBlueBall(ito.getInt("blue"));
                        redArray = ito.getStr("red").split(BaseConstants.SEPARATOR_COMMA);
                        welfareLottery.setRedBall_1(Integer.parseInt(redArray[0]));
                        welfareLottery.setRedBall_2(Integer.parseInt(redArray[1]));
                        welfareLottery.setRedBall_3(Integer.parseInt(redArray[2]));
                        welfareLottery.setRedBall_4(Integer.parseInt(redArray[3]));
                        welfareLottery.setRedBall_5(Integer.parseInt(redArray[4]));
                        welfareLottery.setRedBall_6(Integer.parseInt(redArray[5]));

                        newLotteryList.add(welfareLottery);
                    }
                }
                if(!CollectionUtils.isEmpty(newLotteryList)) {
                    Collections.reverse(newLotteryList);
                    welfareLotteryRepository.saveAll(newLotteryList);
                }
                log.info("crawlData Thread {} end {} successful", Thread.currentThread().getName(), DateUtil.now());
            }else {
                log.error("crawlData failed {}", jsonObj.getStr("message"));
            }
        }
    }
}
