package com.digoak.ssss.lottery.repository;

import com.digoak.ssss.lottery.domain.WelfareLottery;
import io.micronaut.data.annotation.Repository;
import io.micronaut.data.repository.CrudRepository;
import io.micronaut.data.repository.jpa.JpaSpecificationExecutor;

@Repository
public interface WelfareLotteryRepository extends CrudRepository<WelfareLottery, Long>, JpaSpecificationExecutor<WelfareLottery> {

    Long findMaxId();
}
