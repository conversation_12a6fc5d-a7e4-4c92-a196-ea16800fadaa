package com.digoak.ssss.lottery.domain;

import io.swagger.v3.oas.annotations.media.SchemaProperty;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/9
 * @description 双色球
 */
@Data
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@Entity
@Table(name = "welfare_lottery")
public class WelfareLottery {

    @Id
    private Long id;

    @SchemaProperty(name = "开奖时间")
    @NotNull
    @Column(nullable = false, unique = true)
    private Date drawingTime;

    @SchemaProperty(name = "红1")
    private Integer redBall_1;

    @SchemaProperty(name = "红2")
    private Integer redBall_2;

    @SchemaProperty(name = "红3")
    private Integer redBall_3;

    @SchemaProperty(name = "红4")
    private Integer redBall_4;

    @SchemaProperty(name = "红5")
    private Integer redBall_5;

    @SchemaProperty(name = "红6")
    private Integer redBall_6;

    @SchemaProperty(name = "蓝球")
    private Integer blueBall;
}
