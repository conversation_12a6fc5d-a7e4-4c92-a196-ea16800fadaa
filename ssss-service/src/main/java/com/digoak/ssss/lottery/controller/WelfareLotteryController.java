package com.digoak.ssss.lottery.controller;

import com.digoak.ssss.lottery.service.WelfareLotteryService;
import io.micronaut.core.async.annotation.SingleResult;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.validation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Tag(name = "爬虫接口")
@RequiredArgsConstructor
@Controller("/lottery")
@Slf4j
@Validated
public class WelfareLotteryController {

    private final WelfareLotteryService welfareLotteryService;

    @Get(value = "/crawlData", produces = MediaType.APPLICATION_JSON)
    @SingleResult
    public HttpResponse<String> crawlData() {
        welfareLotteryService.crawlData(null, null);
        return HttpResponse.ok("ok");
    }
}
