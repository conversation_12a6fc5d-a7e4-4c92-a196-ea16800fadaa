package com.digoak.ssss.lottery.crontab;

import com.digoak.ssss.lottery.service.WelfareLotteryService;
import io.micronaut.scheduling.annotation.Scheduled;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.micronaut.SchedulerLock;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/16
 * @description 彩票定时任务
 */
@Slf4j
@Singleton
public class LotteryTask {

    @Inject
    private WelfareLotteryService welfareLotteryService;

    @Scheduled(cron = "2 6 2 ? * MON,WED,FRI")
    @SchedulerLock(name = "crawlData")
    public void crawlData() {
        log.info("begin crawlData job {}", new Date());

        welfareLotteryService.crawlData(null, null);

        log.info("end crawlData job {}", new Date());
    }
}
