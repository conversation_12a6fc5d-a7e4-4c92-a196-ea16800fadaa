package com.digoak.ssss;

import io.micronaut.runtime.EmbeddedApplication;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

@MicronautTest
class OakSsssTest {

    @Inject
    EmbeddedApplication<?> application;

    @Test
    void testItWorks() {
        System.out.println("micronaut application test it works...");
        Assertions.assertTrue(application.isRunning());
    }

}
