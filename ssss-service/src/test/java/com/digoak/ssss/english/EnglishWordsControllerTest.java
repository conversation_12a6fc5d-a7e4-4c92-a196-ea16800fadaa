package com.digoak.ssss.english;

import io.micronaut.http.client.HttpClient;
import io.micronaut.http.client.annotation.Client;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

@MicronautTest
public class EnglishWordsControllerTest {

    @Inject
    @Client("/oak-ssss")
    HttpClient client;

    @Test
    public void testIndex() {
        String retrieve = client.toBlocking().retrieve("/englishWords");
        assertEquals("Hello World", retrieve);
    }
}
