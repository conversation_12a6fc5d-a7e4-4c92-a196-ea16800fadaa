package com.digoak.ssss.english;

import com.digoak.ssss.dns.factory.QueryUsableFactory;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;

@MicronautTest
public class DomainControllerTest {

    @Inject
    QueryUsableFactory queryUsableFactory;

    @Test
    public void testDomain() {
        queryUsableFactory.queryUsableDomain("NowDomainService", "abc.com,liselilise.com");
    }
}
