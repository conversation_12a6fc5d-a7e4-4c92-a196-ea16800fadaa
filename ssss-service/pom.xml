<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ssss-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>${packaging}</packaging>

    <parent>
        <groupId>com.digoak.ssss</groupId>
        <artifactId>oak-ssss</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <packaging>jar</packaging>
        <jdk.version>17</jdk.version>
        <release.version>17</release.version>
        <maven.deploy.skip>true</maven.deploy.skip>
        <micronaut.runtime>netty</micronaut.runtime>
        <micronaut.test.resources.enabled>true</micronaut.test.resources.enabled>
        <micronaut.aot.enabled>false</micronaut.aot.enabled>
        <micronaut.aot.packageName>micronaut.demo.aot.generated</micronaut.aot.packageName>
        <exec.mainClass>com.digoak.ssss.Application</exec.mainClass>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.micronaut.data</groupId>
            <artifactId>micronaut-data-hibernate-jpa</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 单元测试 -->
        <dependency>
            <groupId>io.micronaut.test</groupId>
            <artifactId>micronaut-test-junit5</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 分布式定时任务 -->
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-micronaut</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-jdbc-micronaut</artifactId>
        </dependency>

        <!-- 转html -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.21.2</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>io.micronaut.maven</groupId>
                <artifactId>micronaut-maven-plugin</artifactId>
                <configuration>
                    <configFile>aot-${packaging}.properties</configFile>
                    <testResourcesEnabled>false</testResourcesEnabled>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <!-- Uncomment to enable incremental compilation -->
                    <!-- <useIncrementalCompilation>false</useIncrementalCompilation> -->

                    <annotationProcessorPaths combine.self="override">
                        <path>
                            <!-- must precede micronaut-inject-java -->
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut</groupId>
                            <artifactId>micronaut-inject-java</artifactId>
                            <version>${micronaut.core.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut.data</groupId>
                            <artifactId>micronaut-data-processor</artifactId>
                            <version>${micronaut.data.version}</version>
                            <exclusions>
                                <exclusion>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-inject</artifactId>
                                </exclusion>
                            </exclusions>
                        </path>
                        <path>
                            <groupId>io.micronaut</groupId>
                            <artifactId>micronaut-graal</artifactId>
                            <version>${micronaut.core.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut</groupId>
                            <artifactId>micronaut-http-validation</artifactId>
                            <version>${micronaut.core.version}</version>
                        </path>
                        <path>
                            <groupId>io.micronaut.validation</groupId>
                            <artifactId>micronaut-validation-processor</artifactId>
                            <version>${micronaut.validation.version}</version>
                            <exclusions>
                                <exclusion>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-inject</artifactId>
                                </exclusion>
                            </exclusions>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-Amicronaut.processing.group=com.digoak.ssss</arg>
                        <arg>-Amicronaut.processing.module=oak-ssss</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
