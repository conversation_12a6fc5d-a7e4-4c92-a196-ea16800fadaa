applicationType: default
defaultPackage: com.digoak.vehicle
testFramework: junit
sourceLanguage: java
buildTool: maven
features: [app-name, data, data-mongodb, http-client, jackson-databind, java, java-application, jdbc-hikari, jul-to-slf4j, junit, junit-platform-suite-engine, liquibase, logback, lombok, maven, maven-enforcer-plugin, micronaut-aot, micronaut-http-validation, netty-server, readme, shade, static-resources, test-resources, yaml, yaml-build]
