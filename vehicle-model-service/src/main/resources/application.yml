micronaut:
  application:
    name: vehicle-model
  server:
    cors:
      enabled: true
    context-path: /vehicle-model
    port: 8080
  http:
    client:
      connect-timeout: 3s
      read-timeout: 5s # 全局读取超时时间
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 20
  test:
    resources:
      enabled: false
    testcontainers:
      enabled: false

jpa:
  default:
    properties:
      hibernate:
        hbm2ddl:
          auto: update
        show_sql: true

# 如果想更详细地分开配置
mongodb:
  databases:
    # 这里的 `default` 是数据源的名称，您可以根据需要定义多个
    default:
      # 这是一个更详细的配置示例
      uri: ********************************************************************
      # 指定连接超时时间（毫秒）
      connectTimeoutMS: 10000
      # 指定套接字超时时间（毫秒）
      socketTimeoutMS: 10000
      # 指定连接池最大连接数
      maxPoolSize: 100
      # 写入策略
      writeConcern: "ACKNOWLEDGED"
      # 读取策略
      readPreference: "PRIMARY"

liquibase:
  enabled: false
  datasources:
    default:
      change-log: classpath:db/liquibase-changelog.xml