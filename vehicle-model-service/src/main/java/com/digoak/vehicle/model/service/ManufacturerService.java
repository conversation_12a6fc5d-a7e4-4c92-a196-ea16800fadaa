package com.digoak.vehicle.model.service;

import com.digoak.vehicle.model.domain.Manufacturer;
import com.digoak.vehicle.model.dto.ManufacturerDTO;
import com.digoak.vehicle.model.dto.ManufacturerQueryCriteria;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import java.util.Optional;

/**
 * 制造商服务接口
 */
@Tag(name = "制造商服务", description = "制造商相关业务逻辑")
public interface ManufacturerService {

    /**
     * 保存制造商
     *
     * @param manufacturerDTO 制造商DTO
     * @return 创建的制造商
     */
    Manufacturer save(ManufacturerDTO manufacturerDTO);

    /**
     * 根据ID查询制造商
     *
     * @param id 制造商ID
     * @return 制造商
     */
    Optional<Manufacturer> findById(Long id);

    /**
     * 查询所有制造商
     * queryCriteria 查询条件
     * @return 制造商列表
     */
    List<Manufacturer> findAll(ManufacturerQueryCriteria queryCriteria);

    /**
     * 删除制造商
     *
     * @param id 制造商ID
     */
    void deleteById(Long id);

    /**
     * 批量删除制造商
     *
     * @param ids 制造商ID列表
     */
    void deleteByIds(List<Long> ids);

    /**
     * 检查制造商是否存在
     *
     * @param id 制造商ID
     * @return 是否存在
     */
    boolean existsById(Long id);
}