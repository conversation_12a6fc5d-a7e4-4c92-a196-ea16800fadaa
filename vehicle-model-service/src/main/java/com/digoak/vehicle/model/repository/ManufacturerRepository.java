package com.digoak.vehicle.model.repository;

import com.digoak.vehicle.model.domain.Manufacturer;
import io.micronaut.data.mongodb.annotation.MongoRepository;
import io.micronaut.data.repository.CrudRepository;
import io.micronaut.data.repository.jpa.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/9/8
 * @Description 制造商数据访问层
 **/
@MongoRepository
public interface ManufacturerRepository extends CrudRepository<Manufacturer, Long>, JpaSpecificationExecutor<Manufacturer> {

    /**
     * 根据制造商名称查找制造商
     * @param name 制造商名称
     * @return 制造商信息
     */
    Optional<Manufacturer> findByName(String name);

    /**
     * 查找所有制造商，按名称排序
     * @return 制造商列表
     */
    List<Manufacturer> findAllOrderByName();

    /**
     * 根据制造商名称模糊查询
     * @param name 制造商名称
     * @return 制造商列表
     */
    List<Manufacturer> findByNameContainsOrderByName(String name);
}