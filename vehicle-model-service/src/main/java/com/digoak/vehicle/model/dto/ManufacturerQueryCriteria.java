package com.digoak.vehicle.model.dto;

import com.digoak.vehicle.util.Query;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 制造商查询条件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Introspected
@Schema(description = "制造商查询条件")
public class ManufacturerQueryCriteria {

    @Query(type = Query.Type.EQUAL)
    @Schema(description = "统一社会信用代码", example = "91370200163567343M")
    private String unifiedCreditCode;

    @Query(type = Query.Type.INNER_LIKE)
    @Schema(description = "制造商名称", example = "宝马汽车")
    private String name;

}