package com.digoak.vehicle.file.domain;

import com.digoak.boot.platform.base.BaseDomain;
import io.micronaut.data.annotation.MappedEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serial;
import java.util.Objects;

/**
 * @Author: leec
 * @Date: 2022/05/010
 * @Description: 业务附件关联
 */
@Data
@MappedEntity(schema = "f_storage_biz")
public class StorageBiz extends BaseDomain {

    @Serial
    private static final long serialVersionUID = 995446548707271272L;

    @Schema(description = "存储ID")
    @Column(name = "storage_id")
    private Long storageId;

    @Schema(description = "业务表ID")
    @Column(name = "biz_id")
    private Long bizId;

    @Schema(description = "业务名称")
    private String bizName;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "业务类别")
    private String bizType;

    @Schema(description = "排序")
    private Integer sorted;

    @Override
    public int hashCode() {
        return Objects.hash(storageId, bizType);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        StorageBiz that = (StorageBiz) o;
        return Objects.equals(storageId, that.storageId) && Objects.equals(bizType, that.bizType);
    }
}
