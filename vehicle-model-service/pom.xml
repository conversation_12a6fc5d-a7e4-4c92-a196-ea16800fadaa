<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.digoak.vehicle</groupId>
  <artifactId>vehicle-model-service</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>${packaging}</packaging>

  <parent>
    <groupId>com.digoak.ssss</groupId>
    <artifactId>oak-ssss</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <properties>
    <packaging>jar</packaging>
    <jdk.version>17</jdk.version>
    <release.version>17</release.version>
    <maven.deploy.skip>true</maven.deploy.skip>
    <micronaut.runtime>netty</micronaut.runtime>
    <micronaut.aot.enabled>false</micronaut.aot.enabled>
    <micronaut.aot.packageName>com.digoak.vehicle.aot.generated</micronaut.aot.packageName>
    <micronaut.test.resources.enabled>false</micronaut.test.resources.enabled>
    <exec.mainClass>com.digoak.vehicle.Application</exec.mainClass>
  </properties>

  <dependencies>
    <dependency>
      <groupId>io.micronaut.data</groupId>
      <artifactId>micronaut-data-mongodb</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mongodb</groupId>
      <artifactId>mongodb-driver-sync</artifactId>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>io.micronaut.aws</groupId>
      <artifactId>micronaut-aws-sdk-v2</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.micronaut.objectstorage</groupId>
      <artifactId>micronaut-object-storage-aws</artifactId>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>io.micronaut.testresources</groupId>
      <artifactId>micronaut-test-resources-client</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- 单元测试 -->
    <dependency>
      <groupId>io.micronaut.test</groupId>
      <artifactId>micronaut-test-junit5</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-suite-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-core</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>io.micronaut.maven</groupId>
        <artifactId>micronaut-maven-plugin</artifactId>
        <configuration>
          <configFile>aot-${packaging}.properties</configFile>
        </configuration>
        <executions>
          <!-- 禁用 GraalVM Native 相关的执行 -->
          <execution>
            <id>native-image</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>native-image-test</id>
            <phase>none</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <!-- Uncomment to enable incremental compilation -->
          <!-- <useIncrementalCompilation>false</useIncrementalCompilation> -->

          <annotationProcessorPaths combine.self="override">
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>io.micronaut</groupId>
              <artifactId>micronaut-inject-java</artifactId>
              <version>${micronaut.core.version}</version>
            </path>
            <path>
              <groupId>io.micronaut.data</groupId>
              <artifactId>micronaut-data-processor</artifactId>
              <version>${micronaut.data.version}</version>
              <exclusions>
                <exclusion>
                  <groupId>io.micronaut</groupId>
                  <artifactId>micronaut-inject</artifactId>
                </exclusion>
              </exclusions>
            </path>
            <path>
              <groupId>io.micronaut.data</groupId>
              <artifactId>micronaut-data-document-processor</artifactId>
              <version>${micronaut.data.version}</version>
            </path>
            <path>
              <groupId>io.micronaut</groupId>
              <artifactId>micronaut-http-validation</artifactId>
              <version>${micronaut.core.version}</version>
            </path>
            <path>
              <groupId>io.micronaut.validation</groupId>
              <artifactId>micronaut-validation-processor</artifactId>
              <version>${micronaut.validation.version}</version>
              <exclusions>
                <exclusion>
                  <groupId>io.micronaut</groupId>
                  <artifactId>micronaut-inject</artifactId>
                </exclusion>
              </exclusions>
            </path>
          </annotationProcessorPaths>
          <compilerArgs>
            <arg>-Amicronaut.processing.group=com.digoak.vehicle</arg>
            <arg>-Amicronaut.processing.module=vehicle-model</arg>
          </compilerArgs>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <!-- GraalVM Native 构建 Profile，默认不激活 -->
    <profile>
      <id>native</id>
      <activation>
        <property>
          <name>native</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.graalvm.buildtools</groupId>
            <artifactId>native-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>build-native</id>
                <goals>
                  <goal>compile-no-fork</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <repositories>
    <repository>
      <id>HzWangdaRepo</id>
      <url>https://mvnrepository.hzwangda.com/repository/maven-public/</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>HzWangdaRepo</id>
      <url>https://mvnrepository.hzwangda.com/repository/maven-public/</url>
    </pluginRepository>
  </pluginRepositories>
</project>
