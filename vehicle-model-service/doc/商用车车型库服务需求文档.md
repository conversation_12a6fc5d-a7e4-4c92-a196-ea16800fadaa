商用车车型库服务需求文档
1. 项目简介
1.1 项目目标
本项目的目标是建立一个全面、准确、易于访问和维护的商用车车型数据库服务 。该服务将为内部业务系统（如销售、售后、零部件管理等）以及潜在的外部合作伙伴（如经销商、维修厂、物流公司等）提供标准化的商用车车型信息查询接口 。通过整合和规范化数据，提升业务效率，减少因信息不一致导致的错误，并为数据分析和决策提供支持 。

2. 功能需求
2.1 车型数据管理
该模块是整个服务的核心，负责数据的增、删、改、查 。
车型录入： 支持手动录入新车型数据 ，包括品牌、系列、车型名称、车辆类型（如牵引车、载货车、客车、专用车等）、厂商 。支持通过 Excel 或 CSV 文件批量上传数据 。录入时需进行数据校验，例如必填项检查、数据格式验证等 。
车型查询： 支持多维度组合查询 ，包括品牌、车系、车辆类型、公告型号、销售状态等 。支持模糊查询，例如，输入部分车型名称或公告型号即可查找相关车型 。
车型更新： 提供对已录入车型数据的编辑和修改功能 。支持对单一车型进行更新，也支持批量更新指定字段 。
车型删除： 提供删除单个或批量车型的功能，删除前需进行二次确认 。

2.2 车型详情信息
每款车型应包含详尽的参数信息 。
基本信息： 品牌、系列、车型名称、车辆类型、公告型号、销售名称、生产状态（在售/停产）、上市时间、厂商指导价 。
尺寸与质量： 长、宽、高、轴距、整备质量、最大总质量、额定载质量 。
动力系统： 发动机品牌、型号、排量、最大功率、最大扭矩、排放标准 。
底盘系统： 变速箱型号、挡位数量、驱动形式、悬架类型、制动系统 。
驾驶室： 驾驶室类型（高顶、平顶）、座椅数、卧铺数量 。
其他参数： 油箱容积、最高车速、百公里油耗等 。
图片与附件： 支持上传车辆外观图、内饰图、公告参数表 PDF 等 。

2.3 数据同步与 API 接口
数据同步： 建立与外部数据源（如工信部公告、主机厂官方数据）的对接机制 ，实现数据的定期自动同步和更新 。当外部数据源更新时，能自动触发数据更新，或提供手动同步的入口 。
API 服务： 提供标准化的 RESTful API 接口，供内部和外部系统调用 。接口需提供详尽的 API 文档 ，并具备认证和鉴权机制，确保只有授权系统能够访问数据 。

2.4 模型设计
品牌： 品牌表，包含品牌名称、品牌介绍、品牌logo等字段 。
厂商： 厂商表，包含厂商名称、厂商介绍、厂商logo、厂商地址等字段 。
车系： 车系表，包含品牌ID、车系名称、车系介绍等字段 。
车型： 车型表，包含车系ID、车型名称、车型介绍、参数列表等字段 。
参数： 参数表，包含参数名称、参数值、参数单位等字段 。
图片： 图片表，包含图片URL、图片描述、关联车型ID等字段 。
附件： 附件表，包含附件类型、附件名称、附件URL、附件描述、关联车型ID等字段 。

他们关系 ：
品牌与厂商： 一个品牌可以有多个厂商 ，例如，解放有一汽解放、青岛解放等厂商 。
品牌与车系： 一个品牌可以有多个车系 ，例如，解放有 J7 8x4、J6V 8x4、J6P 8x4 等车系 。
车系与车型： 一个车系可以有多个车型 ，例如，解放 J6P 8x4 有 J6P 8x4领航版、J6P 8x4质惠版、J6P 6x2质惠版 等车型 。
车型与参数： 一个车型可以有多个参数 ，例如，J6P 6x2质惠版 有 驱动6x2、320马力、国六排放标准、前进档位10档等参数 。
车型与图片： 一个车型可以有多个图片 ，例如，解放 J7 8x4 有正面图、侧面图、内饰图等 。
车型与附件： 一个车型可以有多个附件 ，例如，解放 J7 8x4 有参数表、使用说明书、售后维修手册等 。

3. 技术需求
架构： 采用 Micronaut 框架 。
编程语言： 推荐使用 Java 。
数据库： 使用关系型数据库（如 MySQL）存储车型结构化数据 。考虑使用 Redis 缓存，以实现高效、精准的多维度查询和模糊搜索 。
API 协议： 采用 RESTful API，数据格式为 JSON 。
安全性： API 接口需支持 OAuth 2.0 或 JWT 等认证协议，确保数据访问安全 。
性能： 查询响应时间应控制在 200 毫秒以内 。支持高并发访问，能够处理每秒数百甚至数千次的请求 。

4. 运营与维护
后台管理系统： 提供一个 Web 端的后台管理界面 ，供运营人员进行数据管理、批量导入和数据审核 。后台需具备用户权限管理功能，不同角色拥有不同操作权限 。
日志与监控： 记录所有数据修改操作的日志 。对 API 接口调用情况、系统性能等进行监控 。

5. 验收标准
功能完整性： 所有功能需求都已实现，并能正常运行 。
数据准确性： 车型库数据与官方公告数据一致性达到 99% 以上 。
性能达标： API 接口响应时间满足技术需求中的性能指标 。
文档齐全： 提供完整的 API 文档、部署手册和运维手册 。